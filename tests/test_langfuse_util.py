"""
Tests for LangfuseUtil class.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from app.utils.langfuse_util import LangfuseUtil, get_langfuse_util


class TestLangfuseUtil:
    """Test cases for LangfuseUtil class."""
    
    @patch('app.utils.langfuse_util.settings')
    @patch('app.utils.langfuse_util.Langfuse')
    def test_initialization_success(self, mock_langfuse_class, mock_settings):
        """Test successful initialization of LangfuseUtil."""
        # Setup mock settings
        mock_settings.LANGFUSE_ENABLED = True
        mock_settings.LANGFUSE_SECRET_KEY = "test_secret"
        mock_settings.LANGFUSE_PUBLIC_KEY = "test_public"
        mock_settings.LANGFUSE_HOST = "https://test.langfuse.com"
        mock_settings.LANGFUSE_FLUSH_AT = 1
        mock_settings.LANGFUSE_FLUSH_INTERVAL = 1000
        
        # Setup mock Langfuse instance
        mock_langfuse_instance = Mock()
        mock_langfuse_class.return_value = mock_langfuse_instance
        
        # Create LangfuseUtil instance
        util = LangfuseUtil()
        
        # Verify Langfuse was initialized with correct parameters
        mock_langfuse_class.assert_called_once_with(
            secret_key="test_secret",
            public_key="test_public",
            host="https://test.langfuse.com",
            flush_at=1,
            flush_interval=1000
        )
        
        # Verify util is enabled
        assert util.is_enabled is True
        assert util.langfuse == mock_langfuse_instance
    
    @patch('app.utils.langfuse_util.settings')
    def test_initialization_disabled(self, mock_settings):
        """Test initialization when Langfuse is disabled."""
        mock_settings.LANGFUSE_ENABLED = False
        
        util = LangfuseUtil()
        
        assert util.is_enabled is False
        assert util.langfuse is None
    
    @patch('app.utils.langfuse_util.settings')
    def test_initialization_missing_config(self, mock_settings):
        """Test initialization with missing configuration."""
        mock_settings.LANGFUSE_ENABLED = True
        mock_settings.LANGFUSE_SECRET_KEY = None
        mock_settings.LANGFUSE_PUBLIC_KEY = "test_public"
        mock_settings.LANGFUSE_HOST = "https://test.langfuse.com"
        
        util = LangfuseUtil()
        
        assert util.is_enabled is False
        assert util.langfuse is None
    
    def test_create_generation_disabled(self):
        """Test create_generation when Langfuse is disabled."""
        util = LangfuseUtil()
        util.langfuse = None  # Simulate disabled state
        
        generation = util.create_generation(
            name="test_generation",
            model="test_model",
            system_prompt="Test system prompt",
            user_prompt="Test user prompt"
        )
        
        assert generation is None
    
    def test_create_generation_success(self):
        """Test successful generation creation."""
        util = LangfuseUtil()
        mock_langfuse = Mock()
        mock_generation = Mock()
        mock_langfuse.generation.return_value = mock_generation
        util.langfuse = mock_langfuse
        
        generation = util.create_generation(
            name="test_generation",
            model="test_model",
            system_prompt="Test system prompt",
            user_prompt="Test user prompt",
            model_parameters={"temperature": 0.5}
        )
        
        # Verify generation was called with correct parameters
        expected_messages = [
            {"role": "system", "content": "Test system prompt"},
            {"role": "user", "content": "Test user prompt"}
        ]
        expected_params = {
            "temperature": 0.5,
            "max_tokens": 4000,
            "top_p": 0.9
        }
        
        mock_langfuse.generation.assert_called_once_with(
            name="test_generation",
            model="test_model",
            input=expected_messages,
            model_parameters=expected_params
        )
        
        assert generation == mock_generation
    
    def test_create_generation_with_input_messages(self):
        """Test generation creation with pre-formatted input messages."""
        util = LangfuseUtil()
        mock_langfuse = Mock()
        mock_generation = Mock()
        mock_langfuse.generation.return_value = mock_generation
        util.langfuse = mock_langfuse
        
        input_messages = [
            {"role": "system", "content": "System message"},
            {"role": "user", "content": "User message"}
        ]
        
        generation = util.create_generation(
            name="test_generation",
            model="test_model",
            input_messages=input_messages
        )
        
        mock_langfuse.generation.assert_called_once_with(
            name="test_generation",
            model="test_model",
            input=input_messages,
            model_parameters={
                "temperature": 0.1,
                "max_tokens": 4000,
                "top_p": 0.9
            }
        )
        
        assert generation == mock_generation
    
    def test_end_generation_success(self):
        """Test successful generation ending."""
        util = LangfuseUtil()
        mock_generation = Mock()
        
        usage_details = {
            "input": 100,
            "output": 200,
            "total": 300
        }
        
        util.end_generation(
            generation=mock_generation,
            output="Test output",
            usage_details=usage_details
        )
        
        mock_generation.end.assert_called_once_with(
            output="Test output",
            usage_details=usage_details
        )
    
    def test_end_generation_with_error(self):
        """Test generation ending with error details."""
        util = LangfuseUtil()
        mock_generation = Mock()
        
        util.end_generation(
            generation=mock_generation,
            output="",
            level="ERROR",
            status_message="Test error message"
        )
        
        mock_generation.end.assert_called_once_with(
            output="",
            level="ERROR",
            status_message="Test error message"
        )
    
    def test_end_generation_none(self):
        """Test end_generation with None generation."""
        util = LangfuseUtil()
        
        # Should not raise an exception
        util.end_generation(
            generation=None,
            output="Test output"
        )
    
    def test_flush_success(self):
        """Test successful flush operation."""
        util = LangfuseUtil()
        mock_langfuse = Mock()
        util.langfuse = mock_langfuse
        
        util.flush()
        
        mock_langfuse.flush.assert_called_once()
    
    def test_flush_disabled(self):
        """Test flush when Langfuse is disabled."""
        util = LangfuseUtil()
        util.langfuse = None
        
        # Should not raise an exception
        util.flush()
    
    def test_singleton_instance(self):
        """Test that get_langfuse_util returns singleton instance."""
        instance1 = get_langfuse_util()
        instance2 = get_langfuse_util()
        
        assert instance1 is instance2
