import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import logging
import asyncio
import traceback
from datetime import datetime
from typing import Dict, Any, Optional

from app.utils.textract import process_document_with_textract
from app.llm.bedrock import extract_data_with_structured_text_bedrock

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()  # This will output to terminal/CloudWatch
    ]
)
logger = logging.getLogger(__name__)


class DocumentExtractionProcessor:
    """
    This class handles the complete extraction pipeline:
    - Document analysis using AWS Textract
    - Intelligent data extraction using Bedrock LLM models
    """

    def __init__(self, s3_client =None):
        """
        Initialize the extraction processor.

        Args:
            s3_client: Pre-configured S3 client
        """

        try:
            self.s3_client = s3_client

        except Exception as e:
            logger.error(f"❌ Failed to initialize DocumentExtractionProcessor: {str(e)}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

    
    async def analyze_document_with_textract(self, s3_uri: str) -> Dict[str, Any]:
        """
        Analyze document using AWS Textract for OCR and document analysis.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            dict: Textract analysis results with both plain text and structured text
        """
        return await process_document_with_textract(s3_uri, s3_client=self.s3_client)
    


    async def extract_data_with_bedrock(self, plain_text: str, structured_text: str,
                                                           system_prompt: Optional[str] = None,
                                                           user_prompt: Optional[str] = None,
                                                           temperature: float = 0.1,
                                                           max_tokens: int = 4000,
                                                           top_p: float = 0.9) -> Dict[str, Any]:
        """
        Extract structured data using Bedrock Nova Pro model with both plain text and structured text with coordinates.

        Args:
            plain_text: Plain text content
            structured_text: Structured text with coordinates
            system_prompt: Optional custom system prompt
            user_prompt: Optional custom user prompt
            temperature: Model temperature
            max_tokens: Maximum tokens
            top_p: Top-p sampling

        Returns:
            dict: Extracted structured data using coordinate-aware processing
        """
        return await extract_data_with_structured_text_bedrock(
            plain_text, structured_text, system_prompt, user_prompt, temperature, max_tokens, top_p
        )



    async def process_document_extraction(self, s3_uri: str,
                                        extraction_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Complete document extraction pipeline: Textract + Bedrock

        Args:
            s3_key: S3 key or full S3 URI of the document
            bucket_name: Optional bucket name if s3_key is just the key
            extraction_prompt: Optional custom extraction prompt for Bedrock

        Returns:
            dict: Complete extraction results including metadata
        """

        start_time = datetime.now()

        try:

            # Step 1: Analyze document with Textract
            logger.info("📋 Step 1: Document analysis with Textract")
            textract_result = await self.analyze_document_with_textract(s3_uri)

            # Step 2: Extract text from Textract result
            logger.info("📋 Step 2: Text extraction from Textract results")
            structured_text = textract_result.get('structured_text', '')

            # Step 3: Extract structured data with Bedrock Nova Pro using both plain and structured text
            logger.info("📋 Step 3: Structured data extraction with Bedrock Nova Pro")
            extracted_text = ""
            bedrock_result = await self.extract_data_with_bedrock(
                extracted_text, structured_text, user_prompt=extraction_prompt
            )

            # Compile final results
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            final_result = {
                "extraction_metadata": {
                    "s3_location": s3_uri,
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "textract_processing_method": textract_result.get('textract_metadata', {}).get('processing_method', 'unknown'),
                    "extracted_text_length": len(extracted_text),
                    "structured_text_length": len(structured_text)
                },
                "textract_metadata": textract_result.get('textract_metadata', {}),
                "textract_raw_result": textract_result.get('textract_raw_result', {}),
                "extracted_text": extracted_text,
                "structured_text": structured_text,
                "structured_data": bedrock_result.get('structured_data', {})
            }

            logger.info("🎉 Document extraction pipeline completed successfully!")
            logger.info(f"    Total processing time: {processing_time:.2f} seconds")

            return final_result

        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.error("❌ Document extraction pipeline failed!")
            logger.error(f"    Error: {str(e)}")
            logger.error(f"    Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"    Failure timestamp: {end_time.isoformat()}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            logger.error("="*80)
            raise


# Main function for testing and standalone execution
async def main(s3_uri: str = 's3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf', s3_client=None):
    """
    Main function for testing the extraction pipeline.

    Args:
        s3_uri: S3 URI of the document to process
    """
    logger.info("🎯 Starting Document Extraction Pipeline")
    logger.info(f"    Input S3 URI: {s3_uri}")

    try:
        # Run the extraction
        processor = DocumentExtractionProcessor(s3_client=s3_client)
        result = await processor.process_document_extraction(s3_uri)        

        # Log summary
        logger.info("")
        logger.info("📊 EXTRACTION SUMMARY:")
        logger.info(f"    Vendor: {result['structured_data']['data']}")

        return result

    except Exception as e:
        logger.error("")
        logger.error(f"❌ Pipeline failed with error: {str(e)}")
        logger.error(f"    Traceback: {traceback.format_exc()}")
        raise


if __name__ == "__main__":
    # Example usage
    import sys

    # creating s3 client
    import boto3
    s3_uri = 's3://document-extraction-logistically/temp/input/FW-Carrier Invoice (1).pdf'
    s3_client = boto3.client('s3')

    # Run the extraction pipeline
    asyncio.run(main(s3_uri))
