"""
Example usage of LangfuseUtil class for document data extraction.

This example demonstrates how to use the LangfuseUtil class to track
LLM generations for document data extraction tasks.
"""

import logging
from app.utils.langfuse_util import get_langfuse_util

logger = logging.getLogger(__name__)


def example_document_extraction_with_langfuse():
    """
    Example function showing how to use LangfuseUtil for document extraction.
    This mirrors the pattern from the provided example code.
    """
    
    # Get the Langfuse utility instance
    langfuse_util = get_langfuse_util()
    
    if not langfuse_util.is_enabled:
        logger.warning("Langfuse is not enabled, proceeding without tracking")
        # Continue with your normal processing
        return
    
    # Define prompts (similar to your example)
    system_prompt = """
## Task Summary:
You are a completely obedient accountant who is an expert at structured data extraction from US based invoices. Follow steps mentioned in "Model Instructions".
"""

    user_prompt = """
## Model Instructions:
1. Extract structured data from the given invoice.
"""

    # Model parameters
    model_parameters = {
        "temperature": 0.1,
        "max_tokens": 4000,
        "top_p": 0.9
    }

    # Create a generation
    generation = langfuse_util.create_generation(
        name="generation_extraction",
        model="amazon.nova-pro-v1:0",
        system_prompt=system_prompt,
        user_prompt=user_prompt,
        model_parameters=model_parameters
    )

    try:
        # Simulate calling your LLM (replace this with your actual Bedrock call)
        # response = bedrock_client.converse(...)
        
        # For demonstration, we'll simulate a response
        simulated_response = {
            'output': {
                'message': {
                    'content': [{'text': 'Extracted invoice data: {...}'}]
                }
            },
            'usage': {
                'inputTokens': 150,
                'outputTokens': 300,
                'totalTokens': 450
            }
        }
        
        # Extract content from response
        if 'output' in simulated_response and 'message' in simulated_response['output']:
            content = simulated_response['output']['message']['content']
            if len(content) > 0 and 'text' in content[0]:
                extracted_content = content[0]['text']
                
                # End generation with success
                usage_details = {
                    "input": simulated_response.get('usage', {}).get('inputTokens', 0),
                    "output": simulated_response.get('usage', {}).get('outputTokens', 0),
                    "total": simulated_response.get('usage', {}).get('totalTokens', 0)
                } if 'usage' in simulated_response else None
                
                langfuse_util.end_generation(
                    generation=generation,
                    output=extracted_content,
                    usage_details=usage_details
                )
                
                logger.info("Document extraction completed successfully")
                return extracted_content
        
    except Exception as e:
        # Handle errors and update generation with error details
        langfuse_util.end_generation(
            generation=generation,
            output="",
            level="ERROR",
            status_message=f"Error calling Bedrock: {str(e)}"
        )
        logger.error(f"Error during document extraction: {str(e)}")
        raise
    
    finally:
        # Ensure data is flushed to Langfuse
        langfuse_util.flush()


def example_with_custom_messages():
    """
    Example showing how to use pre-formatted messages instead of separate system/user prompts.
    """
    
    langfuse_util = get_langfuse_util()
    
    if not langfuse_util.is_enabled:
        return
    
    # Pre-formatted messages
    input_messages = [
        {"role": "system", "content": "You are an expert data extractor."},
        {"role": "user", "content": "Extract data from this document: [document content]"},
        {"role": "assistant", "content": "I'll extract the data step by step."},
        {"role": "user", "content": "Please focus on invoice details."}
    ]
    
    # Create generation with custom messages
    generation = langfuse_util.create_generation(
        name="custom_extraction",
        model="gpt-4",
        input_messages=input_messages,
        model_parameters={"temperature": 0.2}
    )
    
    try:
        # Your LLM processing here
        result = "Extracted data: {...}"
        
        # End generation
        langfuse_util.end_generation(
            generation=generation,
            output=result
        )
        
    finally:
        langfuse_util.flush()


if __name__ == "__main__":
    # Run the example
    example_document_extraction_with_langfuse()
