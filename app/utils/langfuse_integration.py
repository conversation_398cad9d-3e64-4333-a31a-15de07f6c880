"""
Langfuse Integration Module for Neurologix Medical Data Assistant

This module provides functionality to integrate <PERSON><PERSON> for prompt management,

    try:   
        message_logger.info("PROMPT: %s | MESSAGES: %s", prompt_name, json.dumps(messages, indent=2))
    except Exception as e:
        logger.warning("Failed to log messages: %s", e), and generation tracking across all LLM calls in the chat service.

"""

import os
import json
import logging
import hashlib
from typing import Dict, Any, Optional, Tuple, List
from dotenv import load_dotenv

from langfuse import Langfuse

from config import load_prompt_config, PROJECT_ROOT


# Setup message logging to logs folder
logs_dir = PROJECT_ROOT / "logs"
logs_dir.mkdir(exist_ok=True)

message_logger = logging.getLogger('langfuse_messages')
message_handler = logging.FileHandler(logs_dir / 'langfuse_messages.log')
message_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
message_logger.addHandler(message_handler)
message_logger.setLevel(logging.INFO)

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class LangfuseSettings:
    """Langfuse configuration settings"""

    def __init__(self):
        self.public_key = os.getenv('LANGFUSE_PUBLIC_KEY')
        self.secret_key = os.getenv('LANGFUSE_SECRET_KEY')
        self.host = os.getenv('LANGFUSE_HOST')
        self.enabled = os.getenv('LANGFUSE_ENABLED', 'false').lower() == 'true'
        self.flush_at = int(os.getenv('LANGFUSE_FLUSH_AT', '5'))
        self.flush_interval = int(os.getenv('LANGFUSE_FLUSH_INTERVAL', '1000'))
        self.prompt_label = os.getenv('LANGFUSE_PROMPT_LABEL', 'development')

        # Individual prompt versions (use latest by default, override with .env)
        self.intent_classification_version = self._get_prompt_version('LANGFUSE_INTENT_CLASSIFICATION_VERSION')
        self.natural_language_version = self._get_prompt_version('LANGFUSE_NATURAL_LANGUAGE_VERSION')
        self.sql_generation_version = self._get_prompt_version('LANGFUSE_SQL_GENERATION_VERSION')
        self.simple_response_version = self._get_prompt_version('LANGFUSE_SIMPLE_RESPONSE_VERSION')
        self.query_refinement_version = self._get_prompt_version('LANGFUSE_QUERY_REFINEMENT_VERSION')

    def _get_prompt_version(self, env_var: str) -> Optional[int]:
        """Get prompt version from environment variable, return None for latest"""
        version_str = os.getenv(env_var)
        if version_str and version_str.lower() != 'latest':
            try:
                return int(version_str)
            except ValueError:
                logger.warning("Invalid version format for %s: %s, using latest", env_var, version_str)
        return None  # Use latest version

    def get_prompt_version(self, prompt_name: str) -> Optional[int]:
        """Get version for specific prompt"""
        version_map = {
            'intent_classification': self.intent_classification_version,
            'natural_language': self.natural_language_version,
            'sql_generation': self.sql_generation_version,
            'simple_response': self.simple_response_version,
            'query_refinement': self.query_refinement_version
        }
        return version_map.get(prompt_name)
    
    def is_configured(self) -> bool:
        """Check if Langfuse is properly configured"""
        return all([
            self.enabled,
            self.public_key,
            self.secret_key,
            self.host
        ])


class LangfuseIntegration:
    """Main class for Langfuse integration"""
    
    def __init__(self):
        self.settings = LangfuseSettings()
        self._client = None
        self._current_trace = None
        
        if self.settings.is_configured():
            self._initialize_client()
        else:
            logger.warning("Langfuse not configured properly. Integration disabled.")
    
    def _initialize_client(self) -> None:
        """Initialize Langfuse client"""
        try:
            self._client = Langfuse(
                public_key=self.settings.public_key,
                secret_key=self.settings.secret_key,
                host=self.settings.host,
                flush_at=self.settings.flush_at,
                flush_interval=self.settings.flush_interval
            )
            logger.info("Langfuse client initialized successfully")
        except Exception as e:
            logger.error("Failed to initialize Langfuse client: %s", e)
            self._client = None
    
    @property
    def is_enabled(self) -> bool:
        """Check if Langfuse integration is enabled and working"""
        return self._client is not None
    
    def create_trace(self, name: str, user_id: Optional[str] = None,
                    session_id: Optional[str] = None, metadata: Optional[Dict] = None) -> Optional[Any]:
        """Create a new trace for tracking LLM operations"""
        if not self.is_enabled:
            return None

        try:
            self._current_trace = self._client.trace(
                name=name,
                user_id=user_id,
                session_id=session_id,
                metadata=metadata or {}
            )
            logger.debug("Created trace: %s", name)
            return self._current_trace
        except Exception as e:
            logger.error("Failed to create trace: %s", e)
            return None
    
    def flush(self) -> None:
        """Flush all pending data to Langfuse"""
        if not self.is_enabled:
            return
        # Langfuse handles background data sending automatically
        logger.debug("Langfuse data will be sent via background tasks")


def log_messages_to_file(prompt_name: str, messages: list, generation_name: str = None):
    """Log messages to file for debugging"""
    try:
        log_entry = {
            "prompt_name": prompt_name,
            "generation_name": generation_name or prompt_name,
            "timestamp": str(logging.Formatter().formatTime(logging.LogRecord("", 0, "", 0, "", (), None))),
            "messages": messages
        }
        message_logger.info("PROMPT: %s | MESSAGES: %s", prompt_name, json.dumps(messages, indent=2))
    except Exception as e:
        logger.warning("Failed to log messages: %s", e)


def get_langfuse_integration() -> LangfuseIntegration:
    """Get singleton instance of LangfuseIntegration"""
    if not hasattr(get_langfuse_integration, '_instance'):
        get_langfuse_integration._instance = LangfuseIntegration()
    return get_langfuse_integration._instance


def create_langfuse_prompt(name: str, prompt_content: str, prompt_type: str = "chat",
                          labels: Optional[List[str]] = None) -> Optional[Any]:
    """
    Create or update a prompt in Langfuse with support for chat-type prompts

    Args:
        name: Unique name of the prompt
        prompt_content: The prompt content (string) - will be converted to appropriate format
        prompt_type: Type of prompt ("text" or "chat") - defaults to "chat"
        labels: List of labels for the prompt

    Returns:
        Prompt object if successful, None otherwise
    """
    integration = get_langfuse_integration()
    if not integration.is_enabled:
        logger.warning("Cannot create prompt '%s' - Langfuse not enabled", name)
        return None

    try:

        if labels is None:
            labels = [integration.settings.prompt_label]

        # Prepare prompt data based on type
        if prompt_type == "chat":
            # For chat prompts, convert string content to message format
            if isinstance(prompt_content, str): 
                prompt_data = [{"role": "system", "content": prompt_content}]
            else:
                prompt_data = prompt_content
        else:
            # For text prompts, use string directly
            prompt_data = prompt_content

        # Create prompt with the updated Langfuse SDK
        # Use production label instead of deprecated is_active
        if "production" not in labels:
            labels.append("production")

        prompt = integration._client.create_prompt(
            name=name,
            prompt=prompt_data,
            type=prompt_type,  # Now supported in newer versions
            labels=labels
        )

        logger.info("✅ %s prompt '%s' created/updated successfully", prompt_type.capitalize(), name)
        return prompt

    except Exception as e:
        logger.error("❌ Error creating %s prompt '%s': %s", prompt_type, name, e)
        # If chat type fails, try as text type for backward compatibility
        if prompt_type == "chat":
            logger.info("Retrying '%s' as text-type prompt...", name)
            return create_langfuse_prompt(name, prompt_content, "text", labels)
        return None


def get_prompt_from_langfuse(name: str, version: Optional[int] = None,
                           label: Optional[str] = None, **kwargs) -> Tuple[Optional[str], Optional[str], Optional[Any]]:
    """
    Fetch and compile prompt from Langfuse with retry logic

    Args:
        name: Prompt name
        version: Specific version to fetch (optional)
        label: Specific label to fetch (optional)
        **kwargs: Variables to compile into the prompt

    Returns:
        Tuple of (compiled_prompt, prompt_type, prompt_object)
    """
    integration = get_langfuse_integration()
    if not integration.is_enabled:
        logger.warning("Cannot fetch prompt '%s' - Langfuse not enabled", name)
        return None, "text", None

    # Retry logic for network issues
    max_retries = 3
    retry_delay = 1  # seconds

    for attempt in range(max_retries):
        try:
            # Determine version to use
            if version is not None:
                # Use explicitly provided version
                prompt = integration._client.get_prompt(name, version=version)
            elif label is not None:
                # Use specific label
                prompt = integration._client.get_prompt(name, label=label)
            else:
                # Use version from .env config or latest
                config_version = integration.settings.get_prompt_version(name)
                if config_version is not None:
                    prompt = integration._client.get_prompt(name, version=config_version)
                    logger.debug("Using configured version %s for prompt '%s'", config_version, name)
                else:
                    prompt = integration._client.get_prompt(name)  # Latest version
                    logger.debug("Using latest version for prompt '%s'", name)

            if prompt is None:
                logger.error("No prompt found for '%s' (attempt %s)", name, attempt + 1)
                if attempt == max_retries - 1:  # Last attempt
                    fallback_prompt = load_prompt_config(name)
                    return fallback_prompt, "text", None
                continue

            # Determine prompt type based on structure
            prompt_type = "chat" if hasattr(prompt, 'prompt') and isinstance(prompt.prompt, list) else "text"

            # Compile prompt with provided variables
            try:
                # First try to compile with provided variables
                compiled_prompt = prompt.compile(**kwargs)
                logger.debug("Raw compiled prompt for '%s': %s - %s...", name, type(compiled_prompt).__name__, str(compiled_prompt)[:100])

            except Exception as compile_error:
                logger.warning("Error compiling prompt '%s' with variables %s: %s", name, list(kwargs.keys()), compile_error)
                # Try compiling without variables as fallback
                try:
                    compiled_prompt = prompt.compile()
                    logger.info("Successfully compiled prompt '%s' without variables", name)
                except Exception as fallback_error:
                    logger.error("Failed to compile prompt '%s' even without variables: %s", name, fallback_error)
                    fallback_prompt = load_prompt_config(name)
                    return fallback_prompt, "text", None

            # Process the compiled prompt based on type
            if prompt_type == "chat":
                # For chat prompts, compile returns the message list
                if isinstance(compiled_prompt, list) and len(compiled_prompt) > 0:
                    # Find system message and return its content
                    for msg in compiled_prompt:
                        if isinstance(msg, dict) and msg.get('role') == 'system':
                            compiled_prompt = msg.get('content', '')
                            break
                    else:
                        # If no system message found, use first message content
                        compiled_prompt = compiled_prompt[0].get('content', '') if compiled_prompt else ''
                elif isinstance(compiled_prompt, str):
                    # Sometimes chat prompts return strings directly
                    pass  # Use as is
                else:
                    logger.warning("Unexpected chat prompt format for '%s': %s", name, type(compiled_prompt).__name__)
                    compiled_prompt = str(compiled_prompt) if compiled_prompt else ''
            else:
                # For text prompts, compile should return the string
                if not isinstance(compiled_prompt, str):
                    logger.warning("Text prompt '%s' returned non-string: %s", name, type(compiled_prompt).__name__)
                    compiled_prompt = str(compiled_prompt) if compiled_prompt else ''

            # Final validation
            if not compiled_prompt or not compiled_prompt.strip():
                logger.error("Empty compiled prompt for '%s', using fallback", name)
                fallback_prompt = load_prompt_config(name)
                return fallback_prompt, "text", None

            logger.debug("Successfully fetched and compiled %s prompt '%s' (ID: %s) - Length: %s", prompt_type, name, getattr(prompt, 'id', 'unknown'), len(compiled_prompt))
            return compiled_prompt, prompt_type, prompt

        except Exception as e:
            logger.warning("Failed to fetch prompt '%s' on attempt %s: %s", name, attempt + 1, e)
            if attempt < max_retries - 1:
                import time
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                logger.error("All %s attempts failed for prompt '%s', using fallback", max_retries, name)
                # Fallback to local prompt
                fallback_prompt = load_prompt_config(name)
                return fallback_prompt, "text", None


def create_generation(trace: Any, name: str, model: str, input_data: Any,
                     prompt_obj: Optional[Any] = None, model_parameters: Optional[Dict] = None,
                     system_prompt: Optional[str] = None) -> Optional[Any]:
    """
    Create a generation within a trace with both system and user messages

    Args:
        trace: The trace object to add generation to
        name: Name of the generation
        model: Model identifier
        input_data: Input data for the generation (user message)
        prompt_obj: Prompt object from Langfuse (optional)
        model_parameters: Model parameters (optional)
        system_prompt: System prompt content to show in trace (optional)

    Returns:
        Generation object if successful, None otherwise
    """
    if trace is None:
        return None

    try:
        # Prepare input messages to show both system and user messages in Langfuse UI

        # Check if input_data is already a message array (like in SQL generation)
        if isinstance(input_data, list) and len(input_data) > 0 and isinstance(input_data[0], dict) and 'role' in input_data[0]:
            # input_data is already a message array, use it directly
            if system_prompt and system_prompt.strip():
                # Prepend system message to existing messages
                input_messages = [{"role": "system", "content": system_prompt}] + input_data
            else:
                # Use existing message array as-is
                input_messages = input_data
        else:
            # input_data is a simple string/object, wrap it as user message
            if system_prompt and system_prompt.strip():
                # Create message array with both system and user messages
                input_messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": str(input_data)}
                ]
            else:
                # Fallback to just user message if no system prompt
                input_messages = [{"role": "user", "content": str(input_data)}]

        # Ensure prompt object is properly linked - v2.6.0 compatible
        generation_params = {
            "name": name,
            "model": model,
            "input": input_messages,  # Use message array to show both system and user
            "model_parameters": model_parameters or {}
        }

        # Only add prompt if it's a valid Langfuse prompt object
        if prompt_obj is not None:
            generation_params["prompt"] = prompt_obj
            logger.debug("Linking prompt to generation: %s", name)
        else:
            logger.debug("No prompt object to link for generation: %s", name)

        generation = trace.generation(**generation_params)

        logger.debug("Created generation: %s with system+user messages, prompt linked: %s", name, prompt_obj is not None)
        return generation
    except Exception as e:
        logger.error("Failed to create generation '%s': %s", name, e)
        logger.error("Prompt object type: %s", type(prompt_obj))
        return None


def end_generation(generation: Any, output: str, status_message: Optional[str] = None) -> None:
    """
    End a generation with output

    Args:
        generation: The generation object to end
        output: The output from the model
        status_message: Optional status message
    """
    if generation is None:
        return

    try:
        generation.end(output=output)
        logger.debug("Generation ended successfully")
    except Exception as e:
        logger.error("Failed to end generation: %s", e)


def get_file_hash(file_path: str) -> str:
    """Get MD5 hash of a file for change detection"""
    try:
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    except Exception as e:
        logger.error("Failed to get hash for %s: %s", file_path, e)
        return ""


def get_prompt_files_info() -> Dict[str, Dict[str, Any]]:
    """Get information about all prompt files including their hashes"""
    prompt_names = ["intent_classification", "natural_language", "sql_generation", "simple_response", "query_refinement", "debug_subqueries", "debug_analysis"]
    files_info = {}

    for prompt_name in prompt_names:
        file_path = PROJECT_ROOT / "config" / f"{prompt_name}_prompt.json"
        if file_path.exists():
            files_info[prompt_name] = {
                "path": str(file_path),
                "hash": get_file_hash(str(file_path)),
                "modified_time": file_path.stat().st_mtime
            }

    return files_info


def load_stored_hashes() -> Dict[str, str]:
    """Load previously stored file hashes"""
    hash_file = PROJECT_ROOT / "config" / ".prompt_hashes.json"
    try:
        if hash_file.exists():
            with open(hash_file, 'r') as f:
                return json.load(f)
    except Exception as e:
        logger.error("Failed to load stored hashes: %s", e)
    return {}


def save_stored_hashes(hashes: Dict[str, str]) -> None:
    """Save file hashes to track changes"""
    hash_file = PROJECT_ROOT / "config" / ".prompt_hashes.json"
    try:
        with open(hash_file, 'w') as f:
            json.dump(hashes, f, indent=2)
    except Exception as e:
        logger.error("Failed to save hashes: %s", e)


def check_prompts_need_sync() -> Tuple[bool, List[str]]:
    """
    Check if prompts need to be synced to Langfuse

    Returns:
        Tuple of (needs_sync, list_of_changed_prompts)
    """
    current_files = get_prompt_files_info()
    stored_hashes = load_stored_hashes()

    changed_prompts = []

    for prompt_name, file_info in current_files.items():
        current_hash = file_info["hash"]
        stored_hash = stored_hashes.get(prompt_name, "")

        if current_hash != stored_hash:
            changed_prompts.append(prompt_name)

    return len(changed_prompts) > 0, changed_prompts


def sync_prompts_to_langfuse(force: bool = False) -> bool:
    """
    Sync local prompt configurations to Langfuse

    Args:
        force: Force sync even if no changes detected

    Returns:
        True if sync was successful, False otherwise
    """
    integration = get_langfuse_integration()
    if not integration.is_enabled:
        logger.warning("Cannot sync prompts - Langfuse not enabled")
        return False

    try:
        needs_sync, changed_prompts = check_prompts_need_sync()

        if not force and not needs_sync:
            logger.info("No prompt changes detected, skipping sync")
            return True

        prompts_to_sync = changed_prompts if not force else ["intent_classification", "natural_language", "sql_generation", "simple_response", "query_refinement", "debug_subqueries", "debug_analysis"]

        success_count = 0
        current_files = get_prompt_files_info()

        for prompt_name in prompts_to_sync:
            try:
                # Load prompt content
                prompt_content = load_prompt_config(prompt_name)

                # No special handling needed for sql_generation anymore
                # trailssample_str variable removed as requested

                # Create prompt in Langfuse as chat type for better UI visibility
                result = create_langfuse_prompt(
                    name=prompt_name,
                    prompt_content=prompt_content,
                    prompt_type="chat",
                    labels=[integration.settings.prompt_label, "production"]
                )

                if result:
                    success_count += 1
                    logger.info("Successfully synced prompt: %s", prompt_name)
                else:
                    logger.error("Failed to sync prompt: %s", prompt_name)

            except Exception as e:
                logger.error("Error syncing prompt '%s': %s", prompt_name, e)

        # Update stored hashes if all syncs were successful
        if success_count == len(prompts_to_sync):
            new_hashes = {name: current_files[name]["hash"] for name in prompts_to_sync if name in current_files}
            stored_hashes = load_stored_hashes()
            stored_hashes.update(new_hashes)
            save_stored_hashes(stored_hashes)
            logger.info("Successfully synced %s prompts to Langfuse", success_count)
            return True
        else:
            logger.warning("Only %s/%s prompts synced successfully", success_count, len(prompts_to_sync))
            return False

    except Exception as e:
        logger.error("Error during prompt sync: %s", e)
        return False


def add_trailssample_variable(sql_prompt: str) -> str:
    """
    Add {trailssample_str} variable to SQL generation prompt

    Args:
        sql_prompt: Original SQL prompt content

    Returns:
        Modified prompt with trailssample_str variable
    """
    try:
        # Replace the placeholder in the prompt with the actual variable
        # This allows the prompt to be compiled with the trailssample_str variable
        modified_prompt = sql_prompt.replace(
            "{trailssample_str}",
            "{{trailssample_str}}"  # Double braces for Langfuse template variable
        )

        # If the placeholder doesn't exist, add it to the prompt
        if "{trailssample_str}" not in sql_prompt and "{{trailssample_str}}" not in sql_prompt:
            # Add the variable reference to the prompt
            modified_prompt = sql_prompt + "\n\n## Sample Trails Data:\n{{trailssample_str}}"

        logger.debug("Added trailssample_str variable to SQL prompt")
        return modified_prompt

    except Exception as e:
        logger.error("Failed to add trailssample_str variable: %s", e)
        return sql_prompt
