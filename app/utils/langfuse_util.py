from langfuse import <PERSON><PERSON>
from datetime import datetime

# loading langfuse keys from env
from app.core.configuration import settings
secret_key = settings.LANGFUSE_SECRET_KEY
public_key = settings.LANGFUSE_PUBLIC_KEY
host = settings.LANGFUSE_HOST

langfuse = Langfuse(
    secret_key=secret_key,
    public_key=public_key,
    host=host,
    flush_at=1,
    flush_interval=1000
)

system_prompt = """
## Task Summary:
You are a completely obedient accountant who is an expert at structured data extraction from US based invoices. Follow steps mentioned in "Model Instructions".
"""

user_prompt = """
## Model Instructions:
1. Extract structured data from the given invoice.
"""

temperature = 0.1
max_tokens = 4000
top_p = 0.9

# Create a standalone generation without trace context
generation = langfuse.generation(
    name="generation_extraction",
    model="amazon.nova-pro-v1:0",
    input=[
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ],
    model_parameters={
        "temperature": temperature,
        "max_tokens": max_tokens,
        "top_p": top_p
    }
)

# Call Bedrock Nova Pro using converse method
try:
    response = bedrock_client.converse(
        modelId="amazon.nova-pro-v1:0",
        system=[{"text": system_prompt}],
        messages=[
            {
                "role": "user",
                "content": [{"text": user_prompt}]
            }
        ],
        inferenceConfig={
            'maxTokens': max_tokens,
            'temperature': temperature,
            'topP': top_p,
        }
    )
    
    # Extract the content from Nova Pro response using converse format
    if 'output' in response and 'message' in response['output']:
        content = response['output']['message']['content']
        if len(content) > 0 and 'text' in content[0]:
            extracted_content = content[0]['text']
            
    # Update generation with response and usage details
    generation.end(
        output=extracted_content,
        usage_details={
            "input": response.get('usage', {}).get('inputTokens', 0),
            "output": response.get('usage', {}).get('outputTokens', 0),
            "total": response.get('usage', {}).get('totalTokens', 0)
        } if 'usage' in response else None
    )
    
    print(extracted_content)
    
except Exception as e:
    # Handle errors and update generation with error details
    generation.end(
        level="ERROR",
        status_message=f"Error calling Bedrock: {str(e)}"
    )
    raise

# Ensure data is flushed to Langfuse
langfuse.flush()
