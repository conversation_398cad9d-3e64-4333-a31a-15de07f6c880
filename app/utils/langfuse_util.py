"""
Langfuse Utility Class

A simple utility class for initializing Langfuse, creating generations, and ending generations.
Inspired by the provided example code for structured data extraction.
"""

from typing import Optional, Dict, Any, List
import logging

from langfuse import Lang<PERSON>
from app.core.configuration import settings

logger = logging.getLogger(__name__)


class LangfuseUtil:
    """
    Simple utility class for Langfuse operations including initialization,
    generation creation, and generation ending.
    """

    def __init__(self):
        """Initialize Langfuse client with settings from configuration."""
        self.langfuse = None
        self._initialize_langfuse()

    def _initialize_langfuse(self) -> None:
        """Initialize Langfuse client using settings from configuration."""
        try:
            if not settings.LANGFUSE_ENABLED:
                logger.info("<PERSON><PERSON> is disabled in settings")
                return

            if not all([settings.LANGFUSE_SECRET_KEY, settings.LANGFUSE_PUBLIC_KEY, settings.LANGFUSE_HOST]):
                logger.warning("Langfuse configuration incomplete - missing required keys")
                return

            self.langfuse = Langfuse(
                secret_key=settings.LANGFUSE_SECRET_KEY,
                public_key=settings.LANGFUSE_PUBLIC_KEY,
                host=settings.LANGFUSE_HOST,
                flush_at=settings.LANGFUSE_FLUSH_AT,
                flush_interval=settings.LANGFUSE_FLUSH_INTERVAL
            )

            logger.info("Langfuse client initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Langfuse: {str(e)}")
            self.langfuse = None

    @property
    def is_enabled(self) -> bool:
        """Check if Langfuse is enabled and properly initialized."""
        return self.langfuse is not None

    def create_generation(
        self,
        name: str,
        model: str,
        system_prompt: Optional[str] = None,
        user_prompt: Optional[str] = None,
        input_messages: Optional[List[Dict[str, str]]] = None,
        model_parameters: Optional[Dict[str, Any]] = None
    ) -> Optional[Any]:
        """
        Create a standalone generation without trace context.

        Args:
            name: Name of the generation (e.g., "generation_extraction")
            model: Model identifier (e.g., "amazon.nova-pro-v1:0")
            system_prompt: System prompt content
            user_prompt: User prompt content
            input_messages: Pre-formatted input messages (alternative to system/user prompts)
            model_parameters: Model parameters like temperature, max_tokens, top_p

        Returns:
            Generation object if successful, None otherwise
        """
        if not self.is_enabled:
            logger.warning("Cannot create generation - Langfuse not enabled")
            return None

        try:
            # Prepare input messages
            if input_messages:
                messages = input_messages
            else:
                messages = []
                if system_prompt:
                    messages.append({"role": "system", "content": system_prompt})
                if user_prompt:
                    messages.append({"role": "user", "content": user_prompt})

            # Default model parameters
            default_params = {
                "temperature": 0.1,
                "max_tokens": 4000,
                "top_p": 0.9
            }

            # Merge with provided parameters
            final_params = {**default_params, **(model_parameters or {})}

            # Create generation
            generation = self.langfuse.generation(
                name=name,
                model=model,
                input=messages,
                model_parameters=final_params
            )

            logger.debug(f"Created generation: {name}")
            return generation

        except Exception as e:
            logger.error(f"Failed to create generation '{name}': {str(e)}")
            return None

    def end_generation(
        self,
        generation: Any,
        output: str,
        usage_details: Optional[Dict[str, int]] = None,
        level: str = "DEFAULT",
        status_message: Optional[str] = None
    ) -> None:
        """
        End a generation with output and usage details.

        Args:
            generation: The generation object to end
            output: The output from the model
            usage_details: Token usage details with keys: input, output, total
            level: Level for the generation (DEFAULT, ERROR, etc.)
            status_message: Optional status message for errors
        """
        if generation is None:
            logger.warning("Cannot end generation - generation object is None")
            return

        try:
            end_params: Dict[str, Any] = {"output": output}

            # Add usage details if provided
            if usage_details:
                end_params["usage_details"] = usage_details

            # Add level and status message for errors
            if level != "DEFAULT":
                end_params["level"] = level

            if status_message:
                end_params["status_message"] = status_message

            generation.end(**end_params)
            logger.debug("Generation ended successfully")

        except Exception as e:
            logger.error(f"Failed to end generation: {str(e)}")

    def flush(self) -> None:
        """Ensure data is flushed to Langfuse."""
        if not self.is_enabled:
            return

        try:
            self.langfuse.flush()
            logger.debug("Langfuse data flushed")
        except Exception as e:
            logger.error(f"Failed to flush Langfuse data: {str(e)}")


# Global instance for easy access
_langfuse_util_instance = None


def get_langfuse_util() -> LangfuseUtil:
    """Get singleton instance of LangfuseUtil."""
    global _langfuse_util_instance
    if _langfuse_util_instance is None:
        _langfuse_util_instance = LangfuseUtil()
    return _langfuse_util_instance