"""
Langfuse Management Module

This module provides management functions for Langfuse integration,
including automatic prompt synchronization and health checks.
"""

import logging
import os
from typing import Dict, Any, Optional
from datetime import datetime

from langfuse_integration import (
    get_langfuse_integration,
    sync_prompts_to_langfuse,
    check_prompts_need_sync,
    get_prompt_files_info
)

logger = logging.getLogger(__name__)


class LangfuseManager:
    """Manager class for Langfuse operations"""
    
    def __init__(self):
        self.integration = get_langfuse_integration()
        self._last_sync_check = None
        self._sync_interval = 300 # 5 minutes
    
    def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on Langfuse integration
        
        Returns:
            Dictionary with health status information
        """
        status = {
            "enabled": self.integration.is_enabled,
            "configured": self.integration.settings.is_configured(),
            "last_sync_check": self._last_sync_check,
            "prompts_status": {}
        }
        
        if self.integration.is_enabled:
            try:
                # Check if prompts need sync
                needs_sync, changed_prompts = check_prompts_need_sync()
                status["prompts_status"] = {
                    "needs_sync": needs_sync,
                    "changed_prompts": changed_prompts,
                    "total_prompts": len(get_prompt_files_info())
                }
            except Exception as e:
                status["prompts_status"] = {
                    "error": str(e)
                }
        
        return status
    
    def auto_sync_prompts(self, force: bool = False) -> bool:
        """
        Automatically sync prompts if needed
        
        Args:
            force: Force sync regardless of change detection
            
        Returns:
            True if sync was performed and successful, False otherwise
        """
        if not self.integration.is_enabled:
            logger.debug("Langfuse not enabled, skipping auto-sync")
            return False
        
        try:
            # Check if enough time has passed since last check
            now = datetime.now()
            if (not force and 
                self._last_sync_check and 
                (now - self._last_sync_check).seconds < self._sync_interval):
                return False
            
            self._last_sync_check = now
            
            # Check if sync is needed
            needs_sync, changed_prompts = check_prompts_need_sync()
            
            if force or needs_sync:
                logger.info("Auto-syncing prompts to Langfuse (force=%s)", force)
                success = sync_prompts_to_langfuse(force=force)
                
                if success:
                    logger.info("Auto-sync completed successfully")
                else:
                    logger.warning("Auto-sync failed")
                
                return success
            else:
                logger.debug("No prompt changes detected, skipping sync")
                return False
                
        except Exception as e:
            logger.error("Error during auto-sync: %s", e)
            return False
    
    def initialize_prompts(self) -> bool:
        """
        Initialize prompts on Langfuse (typically called on first startup)
        
        Returns:
            True if initialization was successful, False otherwise
        """
        if not self.integration.is_enabled:
            logger.warning("Cannot initialize prompts - Langfuse not enabled")
            return False
        
        try:
            logger.info("Initializing prompts on Langfuse...")
            success = sync_prompts_to_langfuse(force=True)
            
            if success:
                logger.info("Prompt initialization completed successfully")
            else:
                logger.error("Prompt initialization failed")
            
            return success
            
        except Exception as e:
            logger.error("Error during prompt initialization: %s", e)
            return False
    
    def get_sync_status(self) -> Dict[str, Any]:
        """
        Get detailed sync status information
        
        Returns:
            Dictionary with sync status details
        """
        try:
            needs_sync, changed_prompts = check_prompts_need_sync()
            files_info = get_prompt_files_info()
            
            return {
                "needs_sync": needs_sync,
                "changed_prompts": changed_prompts,
                "total_prompts": len(files_info),
                "prompt_files": {
                    name: {
                        "path": info["path"],
                        "last_modified": datetime.fromtimestamp(info["modified_time"]).isoformat(),
                        "hash": info["hash"][:8] + "..."  # Truncated hash for display
                    }
                    for name, info in files_info.items()
                },
                "last_sync_check": self._last_sync_check.isoformat() if self._last_sync_check else None
            }
            
        except Exception as e:
            return {
                "error": str(e)
            }


# Global manager instance
_manager_instance = None


def get_langfuse_manager() -> LangfuseManager:
    """Get singleton instance of LangfuseManager"""
    global _manager_instance
    if _manager_instance is None:
        _manager_instance = LangfuseManager()
    return _manager_instance


def startup_sync() -> bool:
    """
    Perform startup sync of prompts
    This should be called during application startup
    
    Returns:
        True if sync was successful or not needed, False if failed
    """
    manager = get_langfuse_manager()
    
    if not manager.integration.is_enabled:
        logger.info("Langfuse not enabled, skipping startup sync")
        return True
    
    try:
        # Check if this is first time setup (no stored hashes)
        needs_sync, changed_prompts = check_prompts_need_sync()
        
        if needs_sync:
            logger.info("Performing startup prompt sync...")
            return manager.auto_sync_prompts(force=False)
        else:
            logger.info("Prompts are up to date, no startup sync needed")
            return True
            
    except Exception as e:
        logger.error("Error during startup sync: %s", e)
        return False
