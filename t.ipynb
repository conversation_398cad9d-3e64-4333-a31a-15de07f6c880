{"cells": [{"cell_type": "code", "execution_count": null, "id": "62e0ceab", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langfuse import Langfuse\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": null, "id": "e3428ff4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8ec781c2", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'Langfuse' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 4\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# imports\u001b[39;00m\n\u001b[0;32m----> 4\u001b[0m langfuse \u001b[38;5;241m=\u001b[39m \u001b[43mLangfuse\u001b[49m(\n\u001b[1;32m      5\u001b[0m     secret_key\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msk-\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m      6\u001b[0m     public_key\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpk-\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m      7\u001b[0m     host\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://langfuse.in\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      8\u001b[0m )\n\u001b[1;32m      9\u001b[0m prompt \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m     10\u001b[0m compiled_prompt \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n", "\u001b[0;31mNameError\u001b[0m: name 'Langfuse' is not defined"]}], "source": ["# imports\n", "from langfuse import Langfuse\n", "from datetime import datetime\n", "\n", "\n", "langfuse = Langfuse(\n", "    secret_key=\"sk-\",\n", "    public_key=\"pk-\",\n", "    host=\"https://langfuse.in\"\n", ")\n", "prompt = None\n", "compiled_prompt = None\n", "try:\n", "    prompt = langfuse.get_prompt(\"movie-critic\", label=\"devtest\")\n", "    compiled_prompt = prompt.compile(criticlevel=\"expert\", movie=\"Avengers Endgame\")\n", "    print(f\"Compiled Prompt: {compiled_prompt}\")\n", "    print(f\"Prompt Name: {prompt.name}, Version: {prompt.version}, Labels: {prompt.labels}\")\n", "except Exception as e:\n", "    print(f\"Error fetching prompt: {e}\")\n", "    # Fallback prompt\n", "    compiled_prompt = \"As an expert movie critic, do you like Dune 2?\"\n", "    print(f\"Using fallback prompt: {compiled_prompt}\")\n", "# Step 2: <PERSON> the LLM call with <PERSON><PERSON>\n", "with langfuse.start_as_current_generation(\n", "    name=\"movie-critic-generation\",\n", "    model=\"gpt-3.5-turbo\",\n", "    prompt=prompt if prompt else None\n", ") as generation:\n", "    # Invoke OpenAI model with the compiled prompt\n", "    llm_response = invoke_openai_model(compiled_prompt)\n", "    if llm_response:\n", "        print(f\"LLM Response: {llm_response}\")\n", "        # Update the generation with the LLM output\n", "        generation.update(\n", "            output=llm_response,\n", "            end_time=datetime.utcnow()\n", "        )\n", "        print(f\"Generation: {generation}\")\n", "    else:\n", "        print(\"Failed to get response from OpenAI model.\")\n", "        generation.update(\n", "            output=\"Error: No response from OpenAI model\",\n", "            end_time=datetime.utcnow()\n", "        )\n", "# Explicitly flush to ensure data is sent to Langfuse\n", "try:\n", "    langfuse.flush()\n", "    print(\"Trace flushed to Langfuse.\")\n", "except Exception as e:\n", "    print(f\"Error flushing trace: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "6e16168b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1e931a87", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "aaca91af", "metadata": {}, "outputs": [], "source": ["import boto3\n", "# loading aws credentials from .env\n", "from app.core.configuration import settings\n", "aws_region = settings.AWS_REGION\n", "aws_access_key_id = settings.AWS_ACCESS_KEY_ID\n", "aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY\n", "\n", "bedrock_client = boto3.client(\n", "    'bedrock-runtime',\n", "    region_name=aws_region,\n", "    aws_access_key_id=aws_access_key_id,\n", "    aws_secret_access_key=aws_secret_access_key\n", ")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "c9f7b002", "metadata": {}, "outputs": [], "source": ["from langfuse import Langfuse\n", "from datetime import datetime\n", "\n", "# loading langfuse keys from env\n", "from app.core.configuration import settings\n", "secret_key = settings.LANGFUSE_SECRET_KEY\n", "public_key = settings.LANGFUSE_PUBLIC_KEY\n", "host = settings.LANGFUSE_HOST\n", "\n", "\n", "langfuse = Langfuse(\n", "    secret_key=secret_key,\n", "    public_key=public_key,\n", "    host=host,\n", "    flush_at=1,\n", "    flush_interval=1000\n", "    \n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "020c2e44", "metadata": {}, "outputs": [], "source": ["trace = langfuse.trace(\n", "    name=\"trace_extraction\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "42ff5d6c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Certainly! Below is an example of how structured data can be extracted from a typical US-based invoice. For the purpose of this example, let's assume the following is the content of the invoice:\n", "\n", "---\n", "\n", "**Invoice**\n", "\n", "**Invoice Number:** INV-12345  \n", "**Invoice Date:** 2023-10-01  \n", "**Due Date:** 2023-10-30  \n", "\n", "**Bill <PERSON>:**  \n", "Name: ABC Corporation  \n", "Address: 123 Main Street, Suite 400  \n", "City: Anytown  \n", "State: CA  \n", "ZIP: 90210  \n", "\n", "**Ship To:**  \n", "Name: XYZ Inc.  \n", "Address: 456 Elm Street  \n", "City: Othercity  \n", "State: NY  \n", "ZIP: 10001  \n", "\n", "**Items:**\n", "\n", "| Item Description     | Quantity | Unit Price | Total Price |\n", "|----------------------|----------|------------|-------------|\n", "| Widget A             | 10       | $50.00     | $500.00     |\n", "| Gadget B             | 5        | $100.00    | $500.00     |\n", "| Thingamajig C        | 2        | $200.00    | $400.00     |\n", "\n", "**Subtotal:** $1,400.00  \n", "**Tax (8.25%)** : $115.50  \n", "**Total:** $1,515.50  \n", "\n", "**Payment Terms:** Net 30  \n", "**Notes:** Thank you for your business!\n", "\n", "---\n", "\n", "### Extracted Structured Data:\n", "\n", "```json\n", "{\n", "  \"invoice_number\": \"INV-12345\",\n", "  \"invoice_date\": \"2023-10-01\",\n", "  \"due_date\": \"2023-10-30\",\n", "  \"bill_to\": {\n", "    \"name\": \"ABC Corporation\",\n", "    \"address\": \"123 Main Street, Suite 400\",\n", "    \"city\": \"Anytown\",\n", "    \"state\": \"CA\",\n", "    \"zip\": \"90210\"\n", "  },\n", "  \"ship_to\": {\n", "    \"name\": \"XYZ Inc.\",\n", "    \"address\": \"456 Elm Street\",\n", "    \"city\": \"Othercity\",\n", "    \"state\": \"NY\",\n", "    \"zip\": \"10001\"\n", "  },\n", "  \"items\": [\n", "    {\n", "      \"item_description\": \"Widget A\",\n", "      \"quantity\": 10,\n", "      \"unit_price\": 50.00,\n", "      \"total_price\": 500.00\n", "    },\n", "    {\n", "      \"item_description\": \"Gadget B\",\n", "      \"quantity\": 5,\n", "      \"unit_price\": 100.00,\n", "      \"total_price\": 500.00\n", "    },\n", "    {\n", "      \"item_description\": \"Thingamajig C\",\n", "      \"quantity\": 2,\n", "      \"unit_price\": 200.00,\n", "      \"total_price\": 400.00\n", "    }\n", "  ],\n", "  \"subtotal\": 1400.00,\n", "  \"tax_rate\": 8.25,\n", "  \"tax_amount\": 115.50,\n", "  \"total\": 1515.50,\n", "  \"payment_terms\": \"Net 30\",\n", "  \"notes\": \"Thank you for your business!\"\n", "}\n", "```\n", "\n", "### Explanation:\n", "\n", "1. **Invoice Metadata:**\n", "   - `invoice_number`, `invoice_date`, and `due_date` are extracted as key-value pairs.\n", "   \n", "2. **Bill <PERSON> and Ship To Information:**\n", "   - Both `bill_to` and `ship_to` are objects containing name, address, city, state, and zip code.\n", "   \n", "3. **Items:**\n", "   - Each item is an object within an array, containing `item_description`, `quantity`, `unit_price`, and `total_price`.\n", "   \n", "4. **Financial Summary:**\n", "   - `subtotal`, `tax_rate`, `tax_amount`, and `total` are extracted.\n", "   \n", "5. **Payment Terms and Notes:**\n", "   - `payment_terms` and `notes` are included as strings.\n"]}], "source": ["system_prompt = \"\"\"\n", "## Task Summary:\n", "\n", "You are a completely obedient accountant who is an expert at structured data extraction from US based invoices. Follow steps mentioned in \"Model Instructions\".\n", "\"\"\"\n", "\n", "user_prompt = \"\"\"\n", "## Model Instructions:\n", "\n", "1. Extract structured data from the given invoice.\n", "\"\"\"\n", "\n", "temperature = 0.1\n", "max_tokens = 4000\n", "top_p = 0.9\n", "\n", "if trace:\n", "    generation = trace.generation(\n", "        name=\"generation_extraction\",\n", "        model=\"amazon.nova-pro-v1:0\",\n", "        input=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        model_parameters={\n", "            \"temperature\": temperature,\n", "            \"max_tokens\": max_tokens,\n", "            \"top_p\": top_p\n", "        }\n", "    )\n", "\n", "# Call Bedrock Nova Pro using converse method\n", "response = bedrock_client.converse(\n", "    modelId=\"amazon.nova-pro-v1:0\",\n", "    system=[{\"text\": system_prompt}],\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [{\"text\": user_prompt}]\n", "        }\n", "    ],\n", "    inferenceConfig={\n", "        'maxTokens': max_tokens,\n", "        'temperature': temperature,\n", "        'topP': top_p,\n", "    }\n", ")\n", "\n", "generation.end(output=response)\n", "\n", "# Extract the content from Nova Pro response using converse format\n", "if 'output' in response and 'message' in response['output']:\n", "    content = response['output']['message']['content']\n", "    if len(content) > 0 and 'text' in content[0]:\n", "        extracted_content = content[0]['text']\n", "\n", "print(extracted_content)\n"]}, {"cell_type": "code", "execution_count": 13, "id": "3554fdcd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Certainly! Below is an example of structured data extraction from a typical US-based invoice. For the purpose of this example, I'll assume the invoice contains the following information:\n", "\n", "---\n", "\n", "**Invoice Example:**\n", "\n", "```\n", "---------------------------------------------------\n", "|                  ABC Corporation                 |\n", "|          1234 Main Street, Anytown, USA          |\n", "|                  Invoice #12345                 |\n", "|              Date: January 1, 2023              |\n", "---------------------------------------------------\n", "\n", "Bill To:\n", "XYZ Company\n", "7890 Business Blvd\n", "Somecity, USA\n", "\n", "Invoice Details:\n", "---------------------------------------------------\n", "| Description        | Quantity | Unit Price | Total     |\n", "---------------------------------------------------\n", "| Widget A           | 10       | $50.00     | $500.00   |\n", "| Widget B           | 5        | $100.00    | $500.00   |\n", "---------------------------------------------------\n", "| Subtotal:          |          |            | $1000.00  |\n", "| Tax (8%):          |          |            | $80.00    |\n", "| Total:             |          |            | $1080.00  |\n", "---------------------------------------------------\n", "```\n", "\n", "---\n", "\n", "**Extracted Structured Data:**\n", "\n", "```json\n", "{\n", "  \"invoice_number\": \"12345\",\n", "  \"invoice_date\": \"2023-01-01\",\n", "  \"billed_to\": {\n", "    \"company_name\": \"XYZ Company\",\n", "    \"address\": \"7890 Business Blvd, Somecity, USA\"\n", "  },\n", "  \"invoice_details\": [\n", "    {\n", "      \"description\": \"Widget A\",\n", "      \"quantity\": 10,\n", "      \"unit_price\": 50.00,\n", "      \"total\": 500.00\n", "    },\n", "    {\n", "      \"description\": \"Widget B\",\n", "      \"quantity\": 5,\n", "      \"unit_price\": 100.00,\n", "      \"total\": 500.00\n", "    }\n", "  ],\n", "  \"subtotal\": 1000.00,\n", "  \"tax_rate\": 0.08,\n", "  \"tax_amount\": 80.00,\n", "  \"total_amount\": 1080.00\n", "}\n", "```\n", "\n", "**Explanation:**\n", "\n", "1. **Invoice Metadata:**\n", "   - `invoice_number`: The unique identifier for the invoice.\n", "   - `invoice_date`: The date the invoice was issued.\n", "   - `billed_to`: Information about the company to which the invoice is addressed, including company name and address.\n", "\n", "2. **Invoice Details:**\n", "   - An array of objects, each representing a line item on the invoice.\n", "     - `description`: A brief description of the item.\n", "     - `quantity`: The number of units of the item.\n", "     - `unit_price`: The price per unit of the item.\n", "     - `total`: The total price for the line item (quantity * unit price).\n", "\n", "3. **Financial Summary:**\n", "   - `subtotal`: The sum of all line item totals before tax.\n", "   - `tax_rate`: The tax rate applied to the subtotal.\n", "   - `tax_amount`: The amount of tax calculated based on the subtotal and tax rate.\n", "   - `total_amount`: The final amount due, including tax.\n", "\n", "This structured data format allows for easy processing, analysis, and integration into accounting systems.\n"]}], "source": ["from langfuse import Langfuse\n", "from datetime import datetime\n", "\n", "# loading langfuse keys from env\n", "from app.core.configuration import settings\n", "secret_key = settings.LANGFUSE_SECRET_KEY\n", "public_key = settings.LANGFUSE_PUBLIC_KEY\n", "host = settings.LANGFUSE_HOST\n", "\n", "langfuse = Langfuse(\n", "    secret_key=secret_key,\n", "    public_key=public_key,\n", "    host=host,\n", "    flush_at=1,\n", "    flush_interval=1000\n", ")\n", "\n", "system_prompt = \"\"\"\n", "## Task Summary:\n", "You are a completely obedient accountant who is an expert at structured data extraction from US based invoices. Follow steps mentioned in \"Model Instructions\".\n", "\"\"\"\n", "\n", "user_prompt = \"\"\"\n", "## Model Instructions:\n", "1. Extract structured data from the given invoice.\n", "\"\"\"\n", "\n", "temperature = 0.1\n", "max_tokens = 4000\n", "top_p = 0.9\n", "\n", "# Create a standalone generation without trace context\n", "generation = langfuse.generation(\n", "    name=\"generation_extraction\",\n", "    model=\"amazon.nova-pro-v1:0\",\n", "    input=[\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}\n", "    ],\n", "    model_parameters={\n", "        \"temperature\": temperature,\n", "        \"max_tokens\": max_tokens,\n", "        \"top_p\": top_p\n", "    }\n", ")\n", "\n", "# Call Bedrock Nova Pro using converse method\n", "try:\n", "    response = bedrock_client.converse(\n", "        modelId=\"amazon.nova-pro-v1:0\",\n", "        system=[{\"text\": system_prompt}],\n", "        messages=[\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": [{\"text\": user_prompt}]\n", "            }\n", "        ],\n", "        inferenceConfig={\n", "            'maxTokens': max_tokens,\n", "            'temperature': temperature,\n", "            'topP': top_p,\n", "        }\n", "    )\n", "    \n", "    # Extract the content from Nova Pro response using converse format\n", "    if 'output' in response and 'message' in response['output']:\n", "        content = response['output']['message']['content']\n", "        if len(content) > 0 and 'text' in content[0]:\n", "            extracted_content = content[0]['text']\n", "            \n", "    # Update generation with response and usage details\n", "    generation.end(\n", "        output=extracted_content,\n", "        usage_details={\n", "            \"input\": response.get('usage', {}).get('inputTokens', 0),\n", "            \"output\": response.get('usage', {}).get('outputTokens', 0),\n", "            \"total\": response.get('usage', {}).get('totalTokens', 0)\n", "        } if 'usage' in response else None\n", "    )\n", "    \n", "    print(extracted_content)\n", "    \n", "except Exception as e:\n", "    # Handle errors and update generation with error details\n", "    generation.end(\n", "        level=\"ERROR\",\n", "        status_message=f\"Error calling Bedrock: {str(e)}\"\n", "    )\n", "    raise\n", "\n", "# Ensure data is flushed to Langfuse\n", "langfuse.flush()\n"]}, {"cell_type": "code", "execution_count": null, "id": "f26afdd2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}